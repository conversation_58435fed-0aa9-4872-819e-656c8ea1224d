import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, Award, Users, Database, Lock, CheckCircle, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';

const CertificateDashboard = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="mx-auto w-20 h-20 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mb-6">
            <Shield className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-5xl font-bold text-white mb-4">Certificate Management System</h1>
          <p className="text-gray-400 text-xl max-w-3xl mx-auto">
             The official Certificate generation and verification system for Cyber Wolf Training courses
          </p>
        </div>

        {/* Main Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* Certificate Verification */}
          <Card className="glassmorphism border-psyco-green-DEFAULT/30 hover:border-psyco-green-DEFAULT/60 transition-all duration-300">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-psyco-green-DEFAULT/20 rounded-full flex items-center justify-center mb-4">
                <Shield className="h-8 w-8 text-psyco-green-DEFAULT" />
              </div>
              <CardTitle className="text-2xl font-bold text-white">Certificate Verification</CardTitle>
              <CardDescription className="text-gray-300">
                Verify the authenticity of any Cyber Wolf Training certificate
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-center space-x-2 text-gray-300">
                  <CheckCircle className="h-5 w-5 text-psyco-green-DEFAULT" />
                  <span>Instant verification</span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-gray-300">
                  <CheckCircle className="h-5 w-5 text-psyco-green-DEFAULT" />
                  <span>Complete certificate details</span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-gray-300">
                  <CheckCircle className="h-5 w-5 text-psyco-green-DEFAULT" />
                  <span>Secure database lookup</span>
                </div>
              </div>
              <Link to="/certificate-verify">
                <Button className="w-full bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white py-3 text-lg">
                  Verify Certificate
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Certificate Creation */}
          <Card className="glassmorphism border-orange-500/30 hover:border-orange-500/60 transition-all duration-300">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-orange-500/20 rounded-full flex items-center justify-center mb-4">
                <Award className="h-8 w-8 text-orange-400" />
              </div>
              <CardTitle className="text-2xl font-bold text-white">Certificate Creation</CardTitle>
              <CardDescription className="text-gray-300">
                Admin panel for creating new certificates (Authorized access only)
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-center space-x-2 text-gray-300">
                  <Lock className="h-5 w-5 text-orange-400" />
                  <span>Secure admin authentication</span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-gray-300">
                  <CheckCircle className="h-5 w-5 text-orange-400" />
                  <span>16-digit unique ID generation</span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-gray-300">
                  <CheckCircle className="h-5 w-5 text-orange-400" />
                  <span>Complete student data management</span>
                </div>
              </div>
              <Link to="/certificate-create">
                <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 text-lg">
                  Admin Panel
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

       

        {/* Certificate ID Format Info */}
        <Card className="glassmorphism">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white text-center">Certificate ID Format</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="inline-block bg-psyco-green-DEFAULT/10 border border-psyco-green-DEFAULT/30 rounded-lg p-6 mb-4">
                <p className="text-3xl font-mono font-bold text-psyco-green-DEFAULT mb-2">
                  CWT1234567890123
                </p>
                <p className="text-gray-400">16-digit Cyber Wolf Training certificate ID</p>
              </div>
              
            </div>
          </CardContent>
        </Card>

        
      </div>
    </div>
  );
};

export default CertificateDashboard;
